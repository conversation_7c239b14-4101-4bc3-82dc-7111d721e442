<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../core.php';

use Modules\Bacheche\Bacheca;
use Modules\Bacheche\Post;

switch (filter('op')) {
    case 'manage-bacheca':

        if( post('idbacheca') ){
            $bacheca = Bacheca::find(post('idbacheca'));
        }else{
            $bacheca = Bacheca::build(post('nome'));
        }
        $bacheca->nome = post('nome');
        $bacheca->sezione = post('sezione');
        $bacheca->id_parent = post('id_parent');
        $bacheca->save();

        flash()->info(tr('Nuova bacheca aggiunta!'));

        break;

    case 'delete_bacheca':

        $dbo->query("DELETE FROM gdr_forum WHERE id=".prepare(post('id_bacheca')));
        flash()->info(tr('Bacheca rimossa!'));

        echo json_encode([
            'response' => true,
            'message' => tr('Bacheca rimossa!'),
        ]);


        break;

    case 'update_risposta':

        $post = Post::find(post('id_post'));
        $post->messaggio = post('messaggio_edit');
        $post->nome = post('nome_edit');
        $post->idpersonaggio = post('idpersonaggio_edit');
        $post->id_utente = post('id_utente');
        $post->save();

        flash()->info(tr('Messaggio modificato!'));

        echo json_encode([
            'response' => true,
            'message' => tr('Messaggio modificato!'),
        ]);

        break;

    case 'add_topic':
        // Creazione di un nuovo topic
        $nome_topic = post('nome_topic') ?: post('titolo') ?: post('nome');

        $post = Post::build(
            post('id_utente'),
            post('idpersonaggio'),
            post('id_forum'),
            $nome_topic,
            'topic'
        );
        $post->messaggio = post('messaggio');
        $post->save();

        flash()->info(tr('Nuovo topic creato!'));

        echo json_encode([
            'response' => true,
            'message' => tr('Nuovo topic creato!'),
        ]);

        break;

    case 'add_risposta':
        // Aggiunta di una risposta a un topic esistente
        $id_topic = post('id_topic');
        $topic = Post::find($id_topic);

        if ($topic && $topic->isTopic()) {
            $post = Post::build(
                post('id_utente'),
                post('idpersonaggio'),
                post('id_forum'),
                post('nome'),
                'reply',
                $id_topic
            );
            $post->messaggio = post('messaggio');
            $post->save();

            flash()->info(tr('Nuova risposta aggiunta!'));

            echo json_encode([
                'response' => true,
                'message' => tr('Nuova risposta aggiunta!'),
            ]);
        } else {
            echo json_encode([
                'response' => false,
                'message' => tr('Topic non trovato!'),
            ]);
        }

        break;

    case 'delete_post':

        $post = Post::find(post('idriga'));

        if ($post) {
            // Se è un topic, cancella anche tutte le risposte
            if ($post->isTopic()) {
                // Cancella tutte le risposte del topic
                Post::where('id_topic', $post->id)->delete();
                flash()->info(tr('Topic e tutte le sue risposte sono stati rimossi!'));
            } else {
                flash()->info(tr('Risposta rimossa!'));
            }

            // Cancella il post/topic principale
            $post->delete();
        }

        echo json_encode([
            'response' => true,
            'message' => tr('Post rimosso!'),
        ]);

        break;
}
