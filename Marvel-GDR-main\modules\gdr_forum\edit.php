<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../core.php';

// Includi gli stili CSS del forum
echo '<link rel="stylesheet" type="text/css" href="'.$rootdir.'/modules/gdr_forum/forum_styles.css">';

$bacheche_on = $dbo->table('gdr_forum')->where('sezione',0)->where('id_parent',0)->get();
$bacheche_off = $dbo->table('gdr_forum')->where('sezione',1)->where('id_parent',0)->get();

echo '
<div class="forum hidden" id="post"></div>';

echo '
<div class="row forum" id="lista">';
if( $user->gruppo=='Amministratori' ){
    echo '
    <div class="col-md-12 text-right">
        <a class="btn btn-primary" onclick="launch_modal(\''.tr('Bacheca').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_bacheca.php?id_module='.$id_module.'\');"><i class="fa fa-plus"></i> '.tr('Aggiungi').'</a>
    </div>';
}
echo '
    <div class="col-md-12 table-container" style="margin-top:1%">
        <table class="table table-bordered">
            <tr>
                <th>'.tr('Baccheche ON').'</th>
            </tr>';
        if( $bacheche_on ){
            echo '
            <tr>
                <td>
                    <table class="table table-bordered">';
                    foreach($bacheche_on as $bacheca){
                        echo '
                        <tr>
                            <td>
                                '.$bacheca->nome;
                            if( $user->gruppo=='Amministratori' ){
                                echo '
                                <div class="pull-right">
                                    <a class="btn btn-warning btn-sm" onclick="launch_modal(\''.tr('Bacheca').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_bacheca.php?id_module='.$id_module.'&idbacheca='.$bacheca->id.'\');"><i class="fa fa-edit"></i></a>
                                    <a class="btn btn-danger btn-sm " onclick="eliminaBacheca('.$bacheca->id.');"><i class="fa fa-trash"></i></a>
                                </div>';
                            }
                        echo ' 
                            </td>
                        </tr>';

                        $figli = $dbo->table('gdr_forum')->where('id_parent',$bacheca->id)->get();
                        if( !$figli->isEmpty() ){
                            echo '
                            <tr>
                                <td>
                                    <table class="table table-bordered">';
                                    foreach($figli as $figlio){
                                        echo '
                                        <tr>
                                            <td class="clickable" onclick="openPost('.$figlio->id.');">
                                                '.$figlio->nome;
                                                if( $user->gruppo=='Amministratori' ){
                                                    echo '
                                                    <div class="pull-right">
                                                        <a class="btn btn-warning btn-sm" onclick="launch_modal(\''.tr('Bacheca').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_bacheca.php?id_module='.$id_module.'&idbacheca='.$figlio->id.'\');"><i class="fa fa-edit"></i></a>
                                                        <a class="btn btn-danger btn-sm " onclick="eliminaBacheca('.$figlio->id.');"><i class="fa fa-trash"></i></a>
                                                    </div>';
                                                }
                                        echo '    
                                            </td>
                                        </tr>';
                                    }
                            echo '
                                    </table>
                                </td>
                            </tr>';
                        }
                    }
            echo '
                    </table>
                </td>
            </tr>';
        }
echo '
            <tr>
                <th>'.tr('Baccheche OFF').'</th>
            </tr>';
        if( $bacheche_off ){
            echo '
            <tr>
                <td>
                    <table class="table table-bordered">';
                    foreach($bacheche_off as $bacheca){
                        echo '
                        <tr>
                            <td>
                                '.$bacheca->nome;
                            if( $user->gruppo=='Amministratori' ){
                                echo '
                                <div class="pull-right">
                                    <a class="btn btn-warning btn-sm" onclick="launch_modal(\''.tr('Bacheca').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_bacheca.php?id_module='.$id_module.'&idbacheca='.$bacheca->id.'\');"><i class="fa fa-edit"></i></a>
                                    <a class="btn btn-danger btn-sm " onclick="eliminaBacheca('.$bacheca->id.');"><i class="fa fa-trash"></i></a>
                                </div>';
                            }
                            echo ' 
                            </td>
                        </tr>';

                        $figli = $dbo->table('gdr_forum')->where('id_parent',$bacheca->id)->get();
                        if( !$figli->isEmpty() ){
                            echo '
                            <tr>
                                <td>
                                    <table class="table table-bordered">';
                                    foreach($figli as $figlio){
                                        echo '
                                        <tr>
                                            <td class="clickable" onclick="openPost('.$figlio->id.');">
                                                '.$figlio->nome;
                                                if( $user->gruppo=='Amministratori' ){
                                                    echo '
                                                    <div class="pull-right">
                                                        <a class="btn btn-warning btn-sm" onclick="launch_modal(\''.tr('Bacheca').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_bacheca.php?id_module='.$id_module.'&idbacheca='.$figlio->id.'\');"><i class="fa fa-edit"></i></a>
                                                        <a class="btn btn-danger btn-sm " onclick="eliminaBacheca('.$figlio->id.');"><i class="fa fa-trash"></i></a>
                                                    </div>';
                                                }
                                        echo ' 
                                            </td>
                                        </tr>';
                                    }
                            echo '
                                    </table>
                                </td>
                            </tr>';
                        }
                    }
            echo '
                    </table>
                </td>
            </tr>';
        }
echo '
        </table>
    </div>
</div>';

echo '
<script>

$(document).ready(function(){
    $(".content-header").hide();
});

function openPost(id){
    $("#lista").hide();
    $("#post").show();

    // Carica la lista dei topic invece dei singoli post
    $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_topics&id_module='.$id_module.'&id_bacheca="+id);
}

function goBack(){
    $("#lista").show();
    $("#post").hide();
}

function eliminaPost(idriga, id_bacheca_or_topic){
    swal({
        title: "'.tr('Rimuovere questo post?').'",
        html: "'.tr('Sei sicuro di volere rimuovere questo post?').' '.tr("L'operazione è irreversibile").'.",
        type: "warning",
        showCancelButton: true,
        confirmButtonText: "'.tr('Sì').'"
    }).then(function () {
        $.ajax({
            url: globals.rootdir + "/actions.php",
            type: "POST",
            dataType: "json",
            data: {
                id_module: '.$id_module.',
                op: "delete_post",
                idriga: idriga,
            },
            success: function (response) {
                // Determina se stiamo visualizzando un topic thread o la lista dei topic
                var currentUrl = window.location.href;
                if ($("#post").find(".topic-header").length > 0) {
                    // Siamo in un thread di topic, ricarica il thread
                    $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_topic_thread&id_module='.$id_module.'&id_topic=" + id_bacheca_or_topic);
                } else {
                    // Siamo nella lista dei topic, ricarica la lista
                    $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_topics&id_module='.$id_module.'&id_bacheca=" + id_bacheca_or_topic);
                }
                renderMessages();
            },
            error: function() {
                renderMessages();
            }
        });
    }).catch(swal.noop);
}

function eliminaBacheca(id_bacheca){
    swal({
        title: "'.tr('Rimuovere questa bacheca?').'",
        html: "'.tr('Sei sicuro di volere rimuovere questa bacheca e tutte quelle che contiene?').' '.tr("L'operazione è irreversibile").'.",
        type: "warning",
        showCancelButton: true,
        confirmButtonText: "'.tr('Sì').'"
    }).then(function () {
        $.ajax({
            url: globals.rootdir + "/actions.php",
            type: "POST",
            dataType: "json",
            data: {
                id_module: '.$id_module.',
                op: "delete_bacheca",
                id_bacheca: id_bacheca,
            },
            success: function (response) {
                location.reload();
                renderMessages();
            },
            error: function() {
                renderMessages();
            }
        });
    }).catch(swal.noop);
}

</script>';