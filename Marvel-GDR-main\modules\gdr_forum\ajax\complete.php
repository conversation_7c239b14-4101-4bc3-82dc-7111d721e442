<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../../core.php';

use Modules\Bacheche\Bacheca;
use Modules\Bacheche\Post;
use Carbon\Carbon;

switch ($resource) {
    case 'get_topics':
        // Visualizza la lista dei topic in una bacheca
        global $rootdir;
        $id_module = get('id_module');
        $user = Auth::user();

        $bacheca = Bacheca::find(get('id_bacheca'));
        $topics = Post::where('id_forum', get('id_bacheca'))
                     ->where('tipo', 'topic')
                     ->orderBy('updated_at', 'desc')
                     ->get();

        echo '
        <div class="row">
            <div class="col-md-4">
                <span class="clickable" onclick="goBack();" style="font-size:14px;"><i class="fa fa-angle-left fa-2x"></i> '.tr('Indietro').'</span>
            </div>
            <div class="col-md-4 text-center">
                <h2>'.$bacheca->nome.'</h2>
            </div>
            <div class="col-md-4 text-right">
                <button class="btn btn-primary" onclick="launch_modal(\''.tr('Nuovo Topic').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_topic.php?id_module='.$id_module.'&id_bacheca='.get('id_bacheca').'\');">
                    <i class="fa fa-plus"></i> '.tr('Nuovo Topic').'
                </button>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12 table-container forum-topic-list" style="margin-top:1%;">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th width="50%">'.tr('Topic').'</th>
                            <th width="15%" class="hidden-xs">'.tr('Autore').'</th>
                            <th width="10%" class="hidden-xs">'.tr('Risposte').'</th>
                            <th width="10%" class="hidden-xs">'.tr('Visualizzazioni').'</th>
                            <th width="15%" class="hidden-xs">'.tr('Ultimo Post').'</th>
                        </tr>
                    </thead>
                    <tbody>';

        if ($topics->isEmpty()) {
            echo '
                        <tr>
                            <td colspan="5" class="text-center text-muted">
                                <i class="fa fa-info-circle"></i> '.tr('Nessun topic presente in questa bacheca').'
                            </td>
                        </tr>';
        } else {
            foreach($topics as $topic) {
                $ultima_risposta = $topic->getUltimaRisposta();
                $numero_risposte = $topic->getNumeroRisposte();

                echo '
                        <tr class="clickable" onclick="openTopic('.$topic->id.');" style="cursor: pointer;">
                            <td>
                                <div class="topic-title">'.$topic->nome.'</div>
                                <small class="topic-author">'.tr('da').' '.$topic->personaggio->nome.' '.$topic->personaggio->cognome.'</small>
                            </td>
                            <td class="hidden-xs">'.$topic->personaggio->nome.' '.$topic->personaggio->cognome.'</td>
                            <td class="text-center stats-cell hidden-xs">'.$numero_risposte.'</td>
                            <td class="text-center stats-cell hidden-xs">'.$topic->visualizzazioni.'</td>
                            <td class="last-post hidden-xs">';

                if ($ultima_risposta) {
                    echo '
                                <small>'.tr('da').' '.$ultima_risposta->personaggio->nome.' '.$ultima_risposta->personaggio->cognome.'<br>
                                '.(new Carbon($ultima_risposta->created_at))->diffForHumans().'</small>';
                } else {
                    echo '
                                <small>'.tr('da').' '.$topic->personaggio->nome.' '.$topic->personaggio->cognome.'<br>
                                '.(new Carbon($topic->created_at))->diffForHumans().'</small>';
                }

                echo '
                            </td>
                        </tr>';
            }
        }

        echo '
                    </tbody>
                </table>
            </div>
        </div>';

        echo '
        <script>
        function openTopic(id_topic) {
            $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_topic_thread&id_module='.$id_module.'&id_topic=" + id_topic + "&id_bacheca='.get('id_bacheca').'");
        }
        </script>';

        break;

    case 'get_topic_thread':
        // Visualizza un topic con tutte le sue risposte
        global $rootdir;
        $id_module = get('id_module');
        $user = Auth::user();

        $topic = Post::find(get('id_topic'));
        if (!$topic || !$topic->isTopic()) {
            echo '<div class="alert alert-danger">'.tr('Topic non trovato!').'</div>';
            break;
        }

        // Incrementa le visualizzazioni
        $topic->incrementaVisualizzazioni();

        $bacheca = Bacheca::find($topic->id_forum);
        $risposte = $topic->risposte;

        echo '
        <div class="row">
            <div class="col-md-4">
                <span class="clickable" onclick="goBackToTopics();" style="font-size:14px;"><i class="fa fa-angle-left fa-2x"></i> '.tr('Torna ai Topic').'</span>
            </div>
            <div class="col-md-8 text-center">
                <h3>'.$topic->nome.'</h3>
                <small class="text-muted">'.tr('in').' '.$bacheca->nome.'</small>
            </div>
        </div>';

        // Mostra il topic principale
        echo '
        <div class="row">
            <div class="col-md-12 table-container" style="margin-top:1%;max-height:60vh;overflow-y:auto;">
                <table class="table table-bordered">';

        $immagine_topic = ($topic->nome==$topic->personaggio->alias ? $topic->personaggio->immagine_chat_alias : $topic->personaggio->immagine_chat);

        echo '
                    <tr>
                        <td rowspan="2" class="text-center" width="7%">';
                        if( !empty($immagine_topic) ){
                            echo '
                            <img src="'.$immagine_topic.'" class="img-circle elevation-2" style="width: 100%;" title="'.$topic->nome.'" />';
                        }else{
                            echo '
                            <img src="'.$rootdir.'/assets/dist/img/user.png" class="img-circle elevation-2" style="width: 100%;" title="'.$topic->nome.'" />';
                        }

                        if($user->gruppo=='Amministratori' || $topic->id_utente==$user->id){
                            echo '
                            <br><br>
                            <a class="btn btn-warning btn-sm" onclick="launch_modal(\''.tr('Modifica').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_post.php?id_module='.$id_module.'&id_post='.$topic->id.'\');"><i class="fa fa-edit"></i></a>
                            <a class="btn btn-danger btn-sm" onclick="eliminaPost('.$topic->id.','.get('id_bacheca').');"><i class="fa fa-trash"></i></a>';
                        }
                echo '
                        </td>
                        <td style="border-bottom:0px solid gray;">
                            <div class="topic-header">
                                <strong>'.$topic->personaggio->nome.' '.$topic->personaggio->cognome.'</strong>
                                <span class="pull-right"><small class="text-muted">'.tr('Topic creato').' '.(new Carbon($topic->created_at))->diffForHumans().'</small></span>
                            </div>
                            <hr style="margin: 10px 0;">
                            <div class="topic-content">
                                '.$topic->messaggio.'
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-right" style="vertical-align:bottom;border-top:0px solid gray;">';
                        if( $topic->created_at!=$topic->updated_at ){
                            echo '
                            <small class="help-block tip text-muted" title="'.Translator::timestampToLocale($topic->updated_at).'">'.tr('Modificato').' '.(new Carbon($topic->updated_at))->diffForHumans().'</small>&emsp;&emsp;';
                        }
                echo '
                            <small class="help-block tip text-muted">'.tr('Visualizzazioni').': '.$topic->visualizzazioni.'</small>
                        </td>
                    </tr>';

        // Mostra le risposte
        foreach($risposte as $risposta) {
            $immagine_risposta = ($risposta->nome==$risposta->personaggio->alias ? $risposta->personaggio->immagine_chat_alias : $risposta->personaggio->immagine_chat);

            echo '
                    <tr>
                        <td rowspan="2" class="text-center" width="7%">';
                        if( !empty($immagine_risposta) ){
                            echo '
                            <img src="'.$immagine_risposta.'" class="img-circle elevation-2" style="width: 100%;" title="'.$risposta->nome.'" />';
                        }else{
                            echo '
                            <img src="'.$rootdir.'/assets/dist/img/user.png" class="img-circle elevation-2" style="width: 100%;" title="'.$risposta->nome.'" />';
                        }

                        if($user->gruppo=='Amministratori' || $risposta->id_utente==$user->id){
                            echo '
                            <br><br>
                            <a class="btn btn-warning btn-sm" onclick="launch_modal(\''.tr('Modifica').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_post.php?id_module='.$id_module.'&id_post='.$risposta->id.'\');"><i class="fa fa-edit"></i></a>
                            <a class="btn btn-danger btn-sm" onclick="eliminaPost('.$risposta->id.','.get('id_topic').');"><i class="fa fa-trash"></i></a>';
                        }
                echo '
                        </td>
                        <td style="border-bottom:0px solid gray;">
                            <div class="reply-header">
                                <strong>'.$risposta->personaggio->nome.' '.$risposta->personaggio->cognome.'</strong>
                                <span class="pull-right"><small class="text-muted">'.tr('Risposta del').' '.(new Carbon($risposta->created_at))->diffForHumans().'</small></span>
                            </div>
                            <hr style="margin: 10px 0;">
                            <div class="reply-content">
                                '.$risposta->messaggio.'
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-right" style="vertical-align:bottom;border-top:0px solid gray;">';
                        if( $risposta->created_at!=$risposta->updated_at ){
                            echo '
                            <small class="help-block tip text-muted" title="'.Translator::timestampToLocale($risposta->updated_at).'">'.tr('Modificato').' '.(new Carbon($risposta->updated_at))->diffForHumans().'</small>';
                        }
                echo '
                        </td>
                    </tr>';
        }

        echo '
                </table>
            </div>
        </div>';

        // Form per aggiungere una risposta
        echo '
        <hr>
        <div class="forum-form">
            <div class="form-title">
                <i class="fa fa-reply"></i> '.tr('Rispondi al Topic').'
            </div>
            <form action="" method="post" id="rispondi_topic-form" enctype="multipart/form-data">
            <input type="hidden" name="backto" value="record-edit">
            <input type="hidden" name="op" value="add_risposta">
            <input type="hidden" name="id_forum" value="'.$topic->id_forum.'">
            <input type="hidden" name="id_topic" value="'.$topic->id.'">
            <input type="hidden" name="id_utente" value="'.$user->id.'">

            <div class="row">';

            if( $user->gruppo=='Amministratori' ){
                echo '
                <div class="col-md-3">
                    {["type":"select", "label":"'.tr('Personaggio').'", "name":"idpersonaggio", "ajax-source":"lista_png", "select-options": '.json_encode(['idpersonaggio' => $user->idpersonaggio, "gruppo" => $user->gruppo]).', "value":"'.$user->idpersonaggio.'" ]}
                </div>';
            }else{
                echo '
                <input type="hidden" name="idpersonaggio" value="'.$user->idpersonaggio.'">';
            }
    echo '
                <div class="col-md-3">
                    {["type":"select", "label":"'.tr('Nome').'", "name":"nome", "value":"'.$user->personaggio->nome." ".$user->personaggio->cognome.'", "ajax-source":"nome_chat", "select-options":'.json_encode(['idpersonaggio' => $user->idpersonaggio]).' ]}
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    '.input([
                        'type' => 'ckeditor',
                        'label' => tr('Messaggio'),
                        'name' => 'messaggio',
                        'required' => 1,
                        'value' => '',
                    ]).'
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 text-right">
                    <button type="button" onclick="rispondiTopic();" class="btn btn-success">
                        <i class="fa fa-reply"></i> '.tr('Rispondi').'
                    </button>
                </div>
            </div>
            </form>
        </div>';

        echo '
        <script>
        $(document).ready(function(){
            init();
        })

        $("#idpersonaggio").on("change",function(){
            updateSelectOption("idpersonaggio", $(this).val());
            session_set("superselect,idpersonaggio", $(this).val(), 0);
            $("#nome").selectReset();
        });

        function goBackToTopics(){
            $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_topics&id_module='.$id_module.'&id_bacheca='.$topic->id_forum.'");
        }

        function rispondiTopic(){
            salvaForm("#rispondi_topic-form", {
                id_module: "'.$id_module.'",
            }).then(function(response) {
                // Ricarica il thread del topic
                $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_topic_thread&id_module='.$id_module.'&id_topic='.$topic->id.'&id_bacheca='.$topic->id_forum.'");
            });
        }

        </script>';

        break;

    case 'get_post':
        global $rootdir;
        $id_module = get('id_module');
        $user = Auth::user();

        $bacheca = Bacheca::find(get('id_bacheca'));
        $posts = Post::where('id_forum',get('id_bacheca'))->get();

        echo '
        <div class="row">
            <div class="col-md-4">
                <span class="clickable" onclick="goBack();" style="font-size:14px;"><i class="fa fa-angle-left fa-2x"></i> '.tr('Indietro').'</span>
            </div>
            <div class="col-md-4 text-center">
                <h2>'.$bacheca->nome.'</h2>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 table-container" style="margin-top:1%;max-height:40vh;">
                <table class="table table-bordered">';
            foreach($posts as $post){
                $immaggine = ($post->nome==$post->personaggio->alias ? $post->personaggio->immagine_chat_alias : $post->personaggio->immagine_chat);

                echo '
                <tr>
                    <td rowspan="2"class="text-center" width="7%">';
                    if( !empty($immaggine) ){
                        echo '
                        <img src="'.$immaggine.'" class="img-circle elevation-2" style="width: 100%;" title="'.$post->nome.'" />';
                    }else{
                        echo '
                        <img src="'.$rootdir.'/assets/dist/img/user.png" class="img-circle elevation-2" style="width: 100%;" title="'.$post->nome.'" />';
                    }

                    if($user->gruppo=='Amministratori' || $post->id_utente==$user->id){
                        echo '
                        <a class="btn btn-warning btn-sm" onclick="launch_modal(\''.tr('Modifica').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_post.php?id_module='.$id_module.'&id_post='.$post->id.'\');"><i class="fa fa-edit"></i></a>
                        <a class="btn btn-danger btn-sm" onclick="eliminaPost('.$post->id.','.get('id_bacheca').');"><i class="fa fa-trash"></i></a>';
                    }
                echo '
                    </td>
                    <td style="border-bottom:0px solid gray;">
                        '.$post->messaggio.'
                    </td>
                </tr>
                <tr>
                    <td class="text-right" style="vertical-align:bottom;border-top:0px solid gray;">';
                    if( $post->created_at!=$post->updated_at ){
                        echo '
                        <small class="help-block tip text-muted" title="'.Translator::timestampToLocale($post->updated_at).'">'.tr('Modificato').' '.(new Carbon($post->updated_at))->diffForHumans().'</small>&emsp;&emsp;';
                    }
                echo '
                        <small class="help-block tip text-muted" title="'.Translator::timestampToLocale($post->created_at).'">'.tr('Creato').' '.(new Carbon($post->created_at))->diffForHumans().'</small>
                    </td>
                </tr>';
            }
        echo '    
                </table>
            </div>
            <hr>
            <form action="" method="post" id="rispondi-form" enctype="multipart/form-data">
                <input type="hidden" name="backto" value="record-edit">
                <input type="hidden" name="op" value="add_risposta">
                <input type="hidden" name="id_forum" value="'.get('id_bacheca').'">
                <input type="hidden" name="id_utente" value="'.$user->id.'">
                
                <div class="row">';

                if( $user->gruppo=='Amministratori' ){
                    echo '
                    <div class="col-md-3">
                        {["type":"select", "label":"", "name":"idpersonaggio", "ajax-source":"lista_png", "select-options": '.json_encode(['idpersonaggio' => $user->idpersonaggio, "gruppo" => $user->gruppo]).', "value":"'.$user->idpersonaggio.'" ]}
                    </div>';
                }else{
                    echo '
                    <input type="hidden" name="idpersonaggio" value="'.$user->idpersonaggio.'">';
                }
        echo '
                    <div class="col-md-3">
                        {["type":"select", "label":"", "name":"nome", "value":"'.$user->personaggio->nome." ".$user->personaggio->cognome.'", "ajax-source":"nome_chat", "select-options":'.json_encode(['idpersonaggio' => $user->idpersonaggio]).' ]}
                    </div>
                </div>

                <div class="col-md-12" style="padding-left:10%;">
                    '.input([
                        'type' => 'ckeditor',
                        'label' => '',
                        'name' => 'messaggio',
                        'required' => 1,
                        'value' => '',
                        'extra' => 'style=\'max-height:40px;\'',
                    ]).'
                </div>
                <div class="col-md-12 text-right">
                    <a type="button" onclick="rispondi();" class="btn btn-default" ><i class="fa fa-send"></i> '.tr('Rispondi').'</a>
                </div>
            </form>
        </div>';

        echo '
        <script>
        $(document).ready(function(){
            init();
        })

        $("#idpersonaggio").on("change",function(){
            updateSelectOption("idpersonaggio", $(this).val());
            session_set("superselect,idpersonaggio", $(this).val(), 0);
            $("#nome").selectReset();
        });

        function rispondi(){
            salvaForm("#rispondi-form", {
                id_module: "'.$id_module.'",
            }).then(function(response) {
                $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_post&id_module'.$id_module.'&id_bacheca='.get('id_bacheca').'");
            });
        }

        </script>';
        
        break;
}
