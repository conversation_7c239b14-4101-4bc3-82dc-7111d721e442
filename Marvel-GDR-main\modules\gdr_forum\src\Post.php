<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

namespace Modules\Bacheche;

use Common\SimpleModelTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Traits\RecordTrait;
use Modules\Schede\Scheda;
use Models\User;

class Post extends Model
{
    use SimpleModelTrait;
    use RecordTrait;
    use SoftDeletes;

    protected $table = 'gdr_forum_post';
    protected $primaryKey = 'id';
    protected $module = 'Bacheche';

    protected $guarded = [];

    public static function build($id_utente, $idpersonaggio, $id_forum, $nome, $tipo = 'reply', $id_topic = null)
    {
        $model = new static();

        $model->id_utente = $id_utente;
        $model->idpersonaggio = $idpersonaggio;
        $model->id_forum = $id_forum;
        $model->nome = $nome;
        $model->tipo = $tipo; // 'topic' o 'reply'
        $model->id_topic = $id_topic; // ID del topic principale per le risposte
        $model->visualizzazioni = 0;
        $model->save();

        return $model;
    }

    public function save(array $options = [])
    {
        return parent::save($options);
    }

    // Attributi Eloquent
    public function getModuleAttribute()
    {
        return 'Bacheche';
    }

    public function personaggio()
    {
        return $this->belongsTo(Scheda::class, 'idpersonaggio');
    }

    public function utente()
    {
        return $this->belongsTo(User::class, 'id_utente');
    }

    // Relazione per ottenere il topic principale (per le risposte)
    public function topic()
    {
        return $this->belongsTo(Post::class, 'id_topic');
    }

    // Relazione per ottenere tutte le risposte di un topic
    public function risposte()
    {
        return $this->hasMany(Post::class, 'id_topic')->where('tipo', 'reply')->orderBy('created_at', 'asc');
    }

    // Metodo per verificare se è un topic
    public function isTopic()
    {
        return $this->tipo === 'topic';
    }

    // Metodo per verificare se è una risposta
    public function isReply()
    {
        return $this->tipo === 'reply';
    }

    // Metodo per ottenere il numero di risposte
    public function getNumeroRisposte()
    {
        if ($this->isTopic()) {
            return $this->risposte()->count();
        }
        return 0;
    }

    // Metodo per incrementare le visualizzazioni
    public function incrementaVisualizzazioni()
    {
        $this->increment('visualizzazioni');
    }

    // Metodo per ottenere l'ultima risposta
    public function getUltimaRisposta()
    {
        if ($this->isTopic()) {
            return $this->risposte()->latest()->first();
        }
        return null;
    }
}
