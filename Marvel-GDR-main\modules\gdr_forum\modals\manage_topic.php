<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../../core.php';
include_once __DIR__.'/../../../../core.php';

use Modules\Bacheche\Bacheca;

$bacheca = Bacheca::find(get('id_bacheca'));

echo '
<form action="" method="post" id="nuovo_topic-form" enctype="multipart/form-data">
    <input type="hidden" name="backto" value="record-edit">
    <input type="hidden" name="op" value="add_topic">
    <input type="hidden" name="id_forum" value="'.get('id_bacheca').'">
    <input type="hidden" name="id_utente" value="'.$user->id.'">

    <div class="row">
        <div class="col-md-12">
            <h4>'.tr('Nuovo Topic in').' "'.$bacheca->nome.'"</h4>
        </div>
    </div>

    <div class="row">';

    if( $user->gruppo=='Amministratori' ){
        echo '
        <div class="col-md-6">
            {["type":"select", "label":"'.tr('Personaggio').'", "name":"idpersonaggio", "ajax-source":"lista_png", "select-options": '.json_encode(['idpersonaggio' => $user->idpersonaggio, "gruppo" => $user->gruppo]).', "value":"'.$user->idpersonaggio.'" ]}
        </div>';
    }else{
        echo '
        <input type="hidden" name="idpersonaggio" value="'.$user->idpersonaggio.'">';
    }
echo '
        <div class="col-md-6">
            {["type":"select", "label":"'.tr('Nome').'", "name":"nome", "value":"'.$user->personaggio->nome." ".$user->personaggio->cognome.'", "ajax-source":"nome_chat", "select-options":'.json_encode(['idpersonaggio' => $user->idpersonaggio]).' ]}
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            {[ "type": "text", "label": "'.tr('Titolo del Topic').'", "name": "titolo", "required": 1 ]}
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            '.input([
                'type' => 'ckeditor',
                'label' => tr('Messaggio'),
                'name' => 'messaggio',
                'required' => 1,
                'value' => '',
            ]).'
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 text-right">
            <button type="button" onclick="creaTopic();" class="btn btn-primary">
                <i class="fa fa-plus"></i> '.tr('Crea Topic').'
            </button>
        </div>
    </div>
</form>';

echo '
<script>$(document).ready(init)</script>';

echo '
<script>
    $(document).ready(function(){
        init();
    });
    
    $("#idpersonaggio").on("change",function(){
        updateSelectOption("idpersonaggio", $(this).val());
        session_set("superselect,idpersonaggio", $(this).val(), 0);
        $("#nome").selectReset();
    });

    function creaTopic(){
        // Aggiungiamo il titolo al campo nome del form
        var titolo = $("#titolo").val();
        if (titolo) {
            // Creiamo un campo hidden per il nome del topic
            $("#nuovo_topic-form").append("<input type=\"hidden\" name=\"nome_topic\" value=\"" + titolo + "\">");
        }

        salvaForm("#nuovo_topic-form", {
            id_module: "'.$id_module.'",
        }).then(function(response) {
            $(".close").trigger("click");
            // Ricarica la lista dei topic
            $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_topics&id_module='.$id_module.'&id_bacheca='.get('id_bacheca').'");
        });
    }
</script>';
