<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * Modulo GDR Forum
 * 
 * Gestisce un sistema di forum per giochi di ruolo con:
 * - Bacheche organizzate in sezioni ON/OFF
 * - Topic di discussione
 * - Sistema di risposte ai topic
 * - Statistiche (visualizzazioni, numero risposte)
 * - Integrazione con sistema personaggi
 */

// Definizione delle strutture del modulo
$structure = [
    'name' => 'Forum GDR',
    'title' => 'Forum GDR',
    'directory' => 'gdr_forum',
    'icon' => 'fa fa-comments',
    'color' => '#3498db',
    'version' => '2.0.0',
    'compatibility' => '2.4.50',
    'order' => 1,
    'enabled' => true,
    'use_notes' => false,
    'use_checklists' => false,
];

// Definizione delle viste del modulo
$views = [
    'Bacheche' => [
        'query' => "
            SELECT 
                gf.id,
                gf.nome,
                gf.sezione,
                gf.id_parent,
                (SELECT COUNT(*) FROM gdr_forum_post gfp WHERE gfp.id_forum = gf.id AND gfp.tipo = 'topic') as num_topics,
                (SELECT COUNT(*) FROM gdr_forum_post gfp WHERE gfp.id_forum = gf.id) as num_posts,
                (SELECT MAX(gfp.updated_at) FROM gdr_forum_post gfp WHERE gfp.id_forum = gf.id) as ultimo_post
            FROM gdr_forum gf 
            WHERE gf.deleted_at IS NULL
            ORDER BY gf.sezione ASC, gf.nome ASC
        ",
        'fields' => [
            'Nome' => 'nome',
            'Sezione' => 'IF(sezione = 0, "ON", "OFF")',
            'Topic' => 'num_topics',
            'Post Totali' => 'num_posts',
            'Ultimo Post' => 'ultimo_post',
        ],
        'search' => [
            'nome',
        ],
        'format' => [
            'ultimo_post' => 'timestamp',
        ],
    ],
];

// Definizione delle azioni del modulo
$operations = [
    'manage-bacheca' => [
        'title' => 'Gestione Bacheca',
        'description' => 'Crea o modifica una bacheca del forum',
        'class' => 'btn btn-info',
        'icon' => 'fa fa-edit',
    ],
    'delete_bacheca' => [
        'title' => 'Elimina Bacheca',
        'description' => 'Elimina una bacheca e tutti i suoi contenuti',
        'class' => 'btn btn-danger',
        'icon' => 'fa fa-trash',
        'confirm' => true,
    ],
    'add_topic' => [
        'title' => 'Nuovo Topic',
        'description' => 'Crea un nuovo topic di discussione',
        'class' => 'btn btn-primary',
        'icon' => 'fa fa-plus',
    ],
    'add_risposta' => [
        'title' => 'Aggiungi Risposta',
        'description' => 'Aggiungi una risposta a un topic esistente',
        'class' => 'btn btn-success',
        'icon' => 'fa fa-reply',
    ],
    'update_risposta' => [
        'title' => 'Modifica Post',
        'description' => 'Modifica un post o una risposta esistente',
        'class' => 'btn btn-warning',
        'icon' => 'fa fa-edit',
    ],
    'delete_post' => [
        'title' => 'Elimina Post',
        'description' => 'Elimina un post o topic (con tutte le risposte)',
        'class' => 'btn btn-danger',
        'icon' => 'fa fa-trash',
        'confirm' => true,
    ],
];

// Definizione dei permessi del modulo
$permissions = [
    'Amministratori' => [
        'r' => true, // lettura
        'w' => true, // scrittura
        'd' => true, // cancellazione
    ],
    'Utenti' => [
        'r' => true, // lettura
        'w' => true, // scrittura (limitata ai propri post)
        'd' => false, // no cancellazione
    ],
];

// Definizione delle relazioni con altri moduli
$relations = [
    'schede' => [
        'type' => 'belongsTo',
        'model' => 'Modules\\Schede\\Scheda',
        'foreign_key' => 'idpersonaggio',
        'description' => 'Personaggio che ha scritto il post',
    ],
    'utenti' => [
        'type' => 'belongsTo', 
        'model' => 'Models\\User',
        'foreign_key' => 'id_utente',
        'description' => 'Utente che ha scritto il post',
    ],
];

// Configurazione delle notifiche
$notifications = [
    'new_topic' => [
        'title' => 'Nuovo Topic',
        'description' => 'Notifica quando viene creato un nuovo topic',
        'enabled' => true,
    ],
    'new_reply' => [
        'title' => 'Nuova Risposta',
        'description' => 'Notifica quando viene aggiunta una risposta a un topic seguito',
        'enabled' => true,
    ],
];

// Configurazione delle statistiche
$statistics = [
    'total_topics' => [
        'title' => 'Topic Totali',
        'query' => "SELECT COUNT(*) FROM gdr_forum_post WHERE tipo = 'topic' AND deleted_at IS NULL",
        'icon' => 'fa fa-comments',
        'color' => '#3498db',
    ],
    'total_replies' => [
        'title' => 'Risposte Totali', 
        'query' => "SELECT COUNT(*) FROM gdr_forum_post WHERE tipo = 'reply' AND deleted_at IS NULL",
        'icon' => 'fa fa-reply',
        'color' => '#2ecc71',
    ],
    'total_views' => [
        'title' => 'Visualizzazioni Totali',
        'query' => "SELECT SUM(visualizzazioni) FROM gdr_forum_post WHERE tipo = 'topic' AND deleted_at IS NULL",
        'icon' => 'fa fa-eye',
        'color' => '#f39c12',
    ],
    'active_forums' => [
        'title' => 'Bacheche Attive',
        'query' => "SELECT COUNT(*) FROM gdr_forum WHERE sezione = 0 AND deleted_at IS NULL",
        'icon' => 'fa fa-folder-open',
        'color' => '#9b59b6',
    ],
];

// Configurazione del modulo
return [
    'structure' => $structure,
    'views' => $views,
    'operations' => $operations,
    'permissions' => $permissions,
    'relations' => $relations,
    'notifications' => $notifications,
    'statistics' => $statistics,
];
