<?php
/*
 * Script di test per verificare la struttura del forum aggiornato
 * Eseguire questo script per verificare che tutte le modifiche siano state applicate correttamente
 */

include_once __DIR__.'/../../core.php';

use Mo<PERSON><PERSON>\Bacheche\Bacheca;
use Modules\Bacheche\Post;

echo "<h2>Test Struttura Forum GDR</h2>";

// Test 1: Verifica esistenza tabelle
echo "<h3>1. Verifica Tabelle Database</h3>";

try {
    $tables = $dbo->fetchArray("SHOW TABLES LIKE 'gdr_forum%'");
    echo "<p><strong>Tabelle trovate:</strong></p><ul>";
    foreach ($tables as $table) {
        echo "<li>" . array_values($table)[0] . "</li>";
    }
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red;'>Errore nel controllo tabelle: " . $e->getMessage() . "</p>";
}

// Test 2: Verifica struttura tabella gdr_forum_post
echo "<h3>2. Verifica Struttura Tabella gdr_forum_post</h3>";

try {
    $columns = $dbo->fetchArray("DESCRIBE gdr_forum_post");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verifica campi specifici
    $required_fields = ['tipo', 'id_topic', 'visualizzazioni'];
    $existing_fields = array_column($columns, 'Field');
    
    echo "<p><strong>Verifica campi richiesti:</strong></p><ul>";
    foreach ($required_fields as $field) {
        if (in_array($field, $existing_fields)) {
            echo "<li style='color: green;'>✓ Campo '$field' presente</li>";
        } else {
            echo "<li style='color: red;'>✗ Campo '$field' mancante</li>";
        }
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Errore nel controllo struttura: " . $e->getMessage() . "</p>";
}

// Test 3: Verifica indici
echo "<h3>3. Verifica Indici</h3>";

try {
    $indexes = $dbo->fetchArray("SHOW INDEX FROM gdr_forum_post");
    echo "<p><strong>Indici trovati:</strong></p><ul>";
    foreach ($indexes as $index) {
        echo "<li>" . $index['Key_name'] . " su " . $index['Column_name'] . "</li>";
    }
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red;'>Errore nel controllo indici: " . $e->getMessage() . "</p>";
}

// Test 4: Verifica classi PHP
echo "<h3>4. Verifica Classi PHP</h3>";

try {
    // Test classe Bacheca
    if (class_exists('Modules\Bacheche\Bacheca')) {
        echo "<p style='color: green;'>✓ Classe Bacheca caricata correttamente</p>";
    } else {
        echo "<p style='color: red;'>✗ Classe Bacheca non trovata</p>";
    }
    
    // Test classe Post
    if (class_exists('Modules\Bacheche\Post')) {
        echo "<p style='color: green;'>✓ Classe Post caricata correttamente</p>";
        
        // Test metodi della classe Post
        $methods = get_class_methods('Modules\Bacheche\Post');
        $required_methods = ['isTopic', 'isReply', 'getNumeroRisposte', 'incrementaVisualizzazioni', 'getUltimaRisposta'];
        
        echo "<p><strong>Verifica metodi classe Post:</strong></p><ul>";
        foreach ($required_methods as $method) {
            if (in_array($method, $methods)) {
                echo "<li style='color: green;'>✓ Metodo '$method' presente</li>";
            } else {
                echo "<li style='color: red;'>✗ Metodo '$method' mancante</li>";
            }
        }
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>✗ Classe Post non trovata</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Errore nel controllo classi: " . $e->getMessage() . "</p>";
}

// Test 5: Verifica file
echo "<h3>5. Verifica File</h3>";

$required_files = [
    'modals/manage_topic.php',
    'forum_styles.css',
    'update_forum_database.sql',
    'FORUM_UPGRADE_README.md'
];

echo "<p><strong>Verifica file richiesti:</strong></p><ul>";
foreach ($required_files as $file) {
    $full_path = __DIR__ . '/' . $file;
    if (file_exists($full_path)) {
        echo "<li style='color: green;'>✓ File '$file' presente</li>";
    } else {
        echo "<li style='color: red;'>✗ File '$file' mancante</li>";
    }
}
echo "</ul>";

// Test 6: Test funzionalità base
echo "<h3>6. Test Funzionalità Base</h3>";

try {
    // Conta bacheche
    $bacheche_count = Bacheca::count();
    echo "<p>Numero bacheche: $bacheche_count</p>";
    
    // Conta post totali
    $post_count = Post::count();
    echo "<p>Numero post totali: $post_count</p>";
    
    // Conta topic
    $topic_count = Post::where('tipo', 'topic')->count();
    echo "<p>Numero topic: $topic_count</p>";
    
    // Conta risposte
    $reply_count = Post::where('tipo', 'reply')->count();
    echo "<p>Numero risposte: $reply_count</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Errore nel test funzionalità: " . $e->getMessage() . "</p>";
}

echo "<h3>Conclusione</h3>";
echo "<p>Se tutti i test sopra mostrano segni di spunta verdi (✓), il forum è stato aggiornato correttamente.</p>";
echo "<p>Se ci sono errori (✗), consultare il file FORUM_UPGRADE_README.md per le istruzioni di installazione.</p>";
echo "<p><strong>Nota:</strong> Ricordarsi di eseguire lo script SQL update_forum_database.sql se non è già stato fatto.</p>";
?>
