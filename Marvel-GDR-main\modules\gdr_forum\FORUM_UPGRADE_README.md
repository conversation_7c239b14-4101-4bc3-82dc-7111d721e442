# Aggiornamento Modulo GDR Forum

## Panoramica delle Modifiche

Il modulo `gdr_forum` è stato trasformato da un semplice sistema di bacheche con messaggi a un vero e proprio forum con gestione di **topic** e **risposte**.

## Nuove Funzionalità

### 1. Sistema Topic/Risposte
- **Topic**: Post principali che aprono una discussione
- **Risposte**: Messaggi di risposta collegati a un topic specifico
- Visualizzazione thread completo con topic + tutte le risposte

### 2. Interfaccia Migliorata
- **Lista Topic**: Visualizzazione tabellare con statistiche (autore, numero risposte, visualizzazioni, ultimo post)
- **Thread View**: Visualizzazione completa di un topic con tutte le sue risposte
- **Navigazione**: Breadcrumb per tornare dalla visualizzazione thread alla lista topic

### 3. Statistiche
- Contatore visualizzazioni per ogni topic
- Numero di risposte per topic
- Informazioni sull'ultimo post/risposta

## Modifiche al Database

### Nuovi Campi nella Tabella `gdr_forum_post`
- `tipo` ENUM('topic', 'reply'): Distingue tra topic principali e risposte
- `id_topic` INT: Collega le risposte al topic di appartenenza
- `visualizzazioni` INT: Conta le visualizzazioni del topic

### Indici Aggiunti
- `idx_id_topic`: Per migliorare le query sulle risposte
- `idx_tipo`: Per filtrare rapidamente topic e risposte
- `idx_forum_tipo`: Per query combinate su bacheca e tipo

## File Modificati

### 1. `src/Post.php`
- Aggiornato metodo `build()` per gestire tipo e id_topic
- Aggiunti metodi per relazioni: `topic()`, `risposte()`
- Metodi di utilità: `isTopic()`, `isReply()`, `getNumeroRisposte()`, `incrementaVisualizzazioni()`

### 2. `actions.php`
- Nuova azione `add_topic`: Crea un nuovo topic
- Modificata azione `add_risposta`: Aggiunge risposta a un topic esistente
- Migliorata azione `delete_post`: Cancella topic con tutte le risposte

### 3. `ajax/complete.php`
- Nuovo case `get_topics`: Visualizza lista topic di una bacheca
- Nuovo case `get_topic_thread`: Visualizza topic completo con risposte
- Mantenuto case `get_post` per compatibilità

### 4. `edit.php`
- Modificata funzione `openPost()` per caricare lista topic
- Aggiornata funzione `eliminaPost()` per gestire contesto corretto

### 5. Nuovi File
- `modals/manage_topic.php`: Modale per creare nuovi topic
- `update_forum_database.sql`: Script per aggiornare il database

## Istruzioni per l'Installazione

### 1. Backup del Database
```sql
-- Esegui sempre un backup prima di applicare le modifiche
mysqldump -u username -p database_name gdr_forum_post > backup_forum.sql
```

### 2. Applicare le Modifiche al Database
```sql
-- Esegui lo script SQL fornito
source Marvel-GDR-main/modules/gdr_forum/update_forum_database.sql
```

### 3. Verifica Funzionamento
1. Accedi al modulo Forum
2. Verifica che le bacheche esistenti siano ancora visibili
3. Clicca su una bacheca per vedere la lista topic
4. Prova a creare un nuovo topic
5. Prova ad aggiungere risposte a un topic

## Compatibilità

### Dati Esistenti
- I post esistenti vengono automaticamente convertiti in topic
- Le bacheche esistenti rimangono invariate
- Nessuna perdita di dati durante l'aggiornamento

### Funzionalità Mantenute
- Gestione bacheche ON/OFF
- Struttura gerarchica delle bacheche
- Permessi amministratori/utenti
- Editor CKEditor per i messaggi
- Sistema di personaggi e immagini

## Utilizzo

### Per gli Utenti
1. **Visualizzare Topic**: Clicca su una bacheca per vedere i topic
2. **Creare Topic**: Usa il pulsante "Nuovo Topic" nella lista topic
3. **Rispondere**: Apri un topic e usa il form "Rispondi al Topic"
4. **Navigare**: Usa i link "Indietro" e "Torna ai Topic"

### Per gli Amministratori
- Tutte le funzionalità utente
- Possibilità di modificare/cancellare qualsiasi post
- Gestione bacheche (creazione, modifica, cancellazione)
- Cancellazione topic (rimuove automaticamente tutte le risposte)

## Possibili Sviluppi Futuri

1. **Topic in Evidenza**: Campo `pinned` per topic importanti
2. **Topic Chiusi**: Campo `locked` per impedire nuove risposte
3. **Notifiche**: Sistema di notifica per nuove risposte
4. **Reazioni**: Like/dislike sui post
5. **Ricerca**: Funzionalità di ricerca nei topic
6. **Moderazione**: Strumenti avanzati per moderatori

## Supporto

Per problemi o domande relative a questo aggiornamento, verificare:
1. Che lo script SQL sia stato eseguito correttamente
2. Che tutti i file siano stati aggiornati
3. Che non ci siano errori nei log del server
4. Che i permessi sui file siano corretti
