-- Script SQL per aggiornare la struttura del database del modulo gdr_forum
-- Aggiunge i campi necessari per la gestione di topic e risposte

-- Aggiungi il campo 'tipo' per distinguere tra 'topic' e 'reply'
ALTER TABLE `gdr_forum_post` 
ADD COLUMN `tipo` ENUM('topic', 'reply') NOT NULL DEFAULT 'reply' 
AFTER `messaggio`;

-- Aggiungi il campo 'id_topic' per collegare le risposte ai topic
ALTER TABLE `gdr_forum_post` 
ADD COLUMN `id_topic` INT(11) NULL DEFAULT NULL 
AFTER `tipo`;

-- Aggiungi il campo 'visualizzazioni' per contare le visualizzazioni dei topic
ALTER TABLE `gdr_forum_post` 
ADD COLUMN `visualizzazioni` INT(11) NOT NULL DEFAULT 0 
AFTER `id_topic`;

-- Aggiungi l'indice per il campo id_topic per migliorare le performance
ALTER TABLE `gdr_forum_post` 
ADD INDEX `idx_id_topic` (`id_topic`);

-- Aggiungi l'indice per il campo tipo per migliorare le performance
ALTER TABLE `gdr_forum_post` 
ADD INDEX `idx_tipo` (`tipo`);

-- Aggiungi l'indice composto per id_forum e tipo per migliorare le query sui topic
ALTER TABLE `gdr_forum_post` 
ADD INDEX `idx_forum_tipo` (`id_forum`, `tipo`);

-- Aggiungi la foreign key per id_topic (opzionale, per mantenere l'integrità referenziale)
ALTER TABLE `gdr_forum_post` 
ADD CONSTRAINT `fk_topic_parent` 
FOREIGN KEY (`id_topic`) REFERENCES `gdr_forum_post` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Aggiorna i post esistenti per impostarli come 'topic' se non hanno un parent
-- (assumendo che i post esistenti siano tutti topic principali)
UPDATE `gdr_forum_post` 
SET `tipo` = 'topic' 
WHERE `id_topic` IS NULL;

-- Commenti per future modifiche:
-- Se in futuro si vogliono aggiungere altre funzionalità, considerare:
-- 1. Campo 'pinned' BOOLEAN per topic in evidenza
-- 2. Campo 'locked' BOOLEAN per topic chiusi
-- 3. Campo 'last_activity' TIMESTAMP per ordinamento per attività
-- 4. Tabella separata per le reazioni ai post (like, dislike, ecc.)
-- 5. Tabella per le notifiche di nuove risposte
