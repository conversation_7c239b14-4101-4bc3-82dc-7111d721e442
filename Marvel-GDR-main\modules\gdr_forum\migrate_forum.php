<?php
/*
 * Script di migrazione per il modulo GDR Forum
 * Aggiorna automaticamente la struttura del database
 */

include_once __DIR__.'/../../core.php';

echo "<h2>Migrazione Database Forum GDR</h2>";

try {
    // Inizia transazione
    $dbo->beginTransaction();
    
    echo "<p>Inizio migrazione database...</p>";
    
    // 1. Verifica se i campi esistono già
    echo "<h3>1. Verifica struttura esistente</h3>";
    
    $columns = $dbo->fetchArray("DESCRIBE gdr_forum_post");
    $existing_fields = array_column($columns, 'Field');
    
    $needs_migration = false;
    $required_fields = ['tipo', 'id_topic', 'visualizzazioni'];
    
    foreach ($required_fields as $field) {
        if (!in_array($field, $existing_fields)) {
            $needs_migration = true;
            echo "<p style='color: orange;'>Campo '$field' mancante - sarà aggiunto</p>";
        } else {
            echo "<p style='color: green;'>Campo '$field' già presente</p>";
        }
    }
    
    if (!$needs_migration) {
        echo "<p style='color: green;'><strong>La struttura del database è già aggiornata!</strong></p>";
        $dbo->rollBack();
        exit;
    }
    
    // 2. Aggiungi i campi mancanti
    echo "<h3>2. Aggiornamento struttura tabella</h3>";
    
    if (!in_array('tipo', $existing_fields)) {
        $dbo->query("ALTER TABLE `gdr_forum_post` ADD COLUMN `tipo` ENUM('topic', 'reply') NOT NULL DEFAULT 'reply' AFTER `messaggio`");
        echo "<p style='color: green;'>✓ Campo 'tipo' aggiunto</p>";
    }
    
    if (!in_array('id_topic', $existing_fields)) {
        $dbo->query("ALTER TABLE `gdr_forum_post` ADD COLUMN `id_topic` INT(11) NULL DEFAULT NULL AFTER `tipo`");
        echo "<p style='color: green;'>✓ Campo 'id_topic' aggiunto</p>";
    }
    
    if (!in_array('visualizzazioni', $existing_fields)) {
        $dbo->query("ALTER TABLE `gdr_forum_post` ADD COLUMN `visualizzazioni` INT(11) NOT NULL DEFAULT 0 AFTER `id_topic`");
        echo "<p style='color: green;'>✓ Campo 'visualizzazioni' aggiunto</p>";
    }
    
    // 3. Aggiungi indici per migliorare le performance
    echo "<h3>3. Aggiornamento indici</h3>";
    
    try {
        $dbo->query("ALTER TABLE `gdr_forum_post` ADD INDEX `idx_id_topic` (`id_topic`)");
        echo "<p style='color: green;'>✓ Indice 'idx_id_topic' aggiunto</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "<p style='color: blue;'>Indice 'idx_id_topic' già presente</p>";
        } else {
            throw $e;
        }
    }
    
    try {
        $dbo->query("ALTER TABLE `gdr_forum_post` ADD INDEX `idx_tipo` (`tipo`)");
        echo "<p style='color: green;'>✓ Indice 'idx_tipo' aggiunto</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "<p style='color: blue;'>Indice 'idx_tipo' già presente</p>";
        } else {
            throw $e;
        }
    }
    
    try {
        $dbo->query("ALTER TABLE `gdr_forum_post` ADD INDEX `idx_forum_tipo` (`id_forum`, `tipo`)");
        echo "<p style='color: green;'>✓ Indice 'idx_forum_tipo' aggiunto</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "<p style='color: blue;'>Indice 'idx_forum_tipo' già presente</p>";
        } else {
            throw $e;
        }
    }
    
    // 4. Aggiungi foreign key (opzionale)
    echo "<h3>4. Aggiornamento foreign key</h3>";
    
    try {
        $dbo->query("ALTER TABLE `gdr_forum_post` ADD CONSTRAINT `fk_topic_parent` FOREIGN KEY (`id_topic`) REFERENCES `gdr_forum_post` (`id`) ON DELETE CASCADE ON UPDATE CASCADE");
        echo "<p style='color: green;'>✓ Foreign key 'fk_topic_parent' aggiunta</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate foreign key constraint name') !== false || 
            strpos($e->getMessage(), 'already exists') !== false) {
            echo "<p style='color: blue;'>Foreign key 'fk_topic_parent' già presente</p>";
        } else {
            echo "<p style='color: orange;'>Avviso: Non è stato possibile aggiungere la foreign key: " . $e->getMessage() . "</p>";
            // Non bloccare la migrazione per questo
        }
    }
    
    // 5. Migra i dati esistenti
    echo "<h3>5. Migrazione dati esistenti</h3>";
    
    // Imposta tutti i post esistenti come 'topic' se non hanno un parent
    $updated = $dbo->query("UPDATE `gdr_forum_post` SET `tipo` = 'topic' WHERE `id_topic` IS NULL AND `tipo` = 'reply'");
    $affected_rows = $dbo->affectedRows();
    
    if ($affected_rows > 0) {
        echo "<p style='color: green;'>✓ $affected_rows post esistenti convertiti in topic</p>";
    } else {
        echo "<p style='color: blue;'>Nessun post da convertire</p>";
    }
    
    // 6. Verifica finale
    echo "<h3>6. Verifica finale</h3>";
    
    $final_columns = $dbo->fetchArray("DESCRIBE gdr_forum_post");
    $final_fields = array_column($final_columns, 'Field');
    
    $all_present = true;
    foreach ($required_fields as $field) {
        if (in_array($field, $final_fields)) {
            echo "<p style='color: green;'>✓ Campo '$field' verificato</p>";
        } else {
            echo "<p style='color: red;'>✗ Campo '$field' mancante dopo migrazione</p>";
            $all_present = false;
        }
    }
    
    if ($all_present) {
        // Commit della transazione
        $dbo->commit();
        echo "<h3 style='color: green;'>✅ Migrazione completata con successo!</h3>";
        echo "<p>Il database è stato aggiornato correttamente. Il forum è ora pronto per l'uso con le nuove funzionalità.</p>";
        echo "<p><strong>Prossimi passi:</strong></p>";
        echo "<ul>";
        echo "<li>Verifica che tutti i file siano stati caricati correttamente</li>";
        echo "<li>Testa la creazione di nuovi topic</li>";
        echo "<li>Testa l'aggiunta di risposte ai topic</li>";
        echo "<li>Verifica che le statistiche funzionino correttamente</li>";
        echo "</ul>";
    } else {
        $dbo->rollBack();
        echo "<h3 style='color: red;'>❌ Migrazione fallita</h3>";
        echo "<p>Alcuni campi non sono stati creati correttamente. Controllare i permessi del database e riprovare.</p>";
    }
    
} catch (Exception $e) {
    // Rollback in caso di errore
    $dbo->rollBack();
    echo "<h3 style='color: red;'>❌ Errore durante la migrazione</h3>";
    echo "<p style='color: red;'>Errore: " . $e->getMessage() . "</p>";
    echo "<p>La migrazione è stata annullata. Nessuna modifica è stata applicata al database.</p>";
    echo "<p><strong>Possibili soluzioni:</strong></p>";
    echo "<ul>";
    echo "<li>Verificare i permessi del database</li>";
    echo "<li>Controllare che l'utente abbia i privilegi ALTER TABLE</li>";
    echo "<li>Eseguire manualmente lo script SQL update_forum_database.sql</li>";
    echo "</ul>";
}
?>
